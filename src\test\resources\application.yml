# Test Configuration
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driverClassName: org.h2.Driver
    username: sa
    password: password
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        "[format_sql]": true
        dialect: org.hibernate.dialect.H2Dialect
    defer-datasource-initialization: true
  h2:
    console:
      enabled: true
  sql:
    init:
      mode: always
  # WebSocket Configuration
  websocket:
    max-text-message-size: 8192
    max-binary-message-size: 8192

# JWT Configuration (for tests)
jwt:
  secret: testSecretKeyForTestingPurposesOnlyDoNotUseInProduction
  expiration: 86400000

# CORS Configuration
cors:
  allowed-origins: "*"
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

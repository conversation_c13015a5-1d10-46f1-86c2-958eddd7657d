# Server Configuration
server:
  port: 8080
  tomcat:
    max-http-form-post-size: 1GB
    max-swallow-size: 1GB

# Spring Configuration
spring:
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 1GB
      max-request-size: 1GB
      file-size-threshold: 2KB
      enabled: true

  # Database Configuration
  datasource:
    url: *************************************************************************************************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password:

  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        "[format_sql]": true
        dialect: org.hibernate.dialect.MySQL8Dialect
        connection:
          characterEncoding: utf8mb4
          useUnicode: true
          charSet: UTF-8
        # Enable proper handling of BLOB/CLOB types
        jdbc:
          lob:
            "[non_contextual_creation]": true
        # Naming strategy
        "[physical_naming_strategy]": org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        "[implicit_naming_strategy]": org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    defer-datasource-initialization: true

  # SQL Initialization
  sql:
    init:
      mode: always
      schema-locations:
        - classpath:db/update-charset.sql
        - classpath:db/update-message-schema.sql
        - classpath:db/fix-role-prefixes.sql
        - classpath:db/create-notification-tables.sql
        - classpath:db/ensure-profile-picture-column.sql
      continue-on-error: true

  # WebSocket Configuration
  websocket:
    max-text-message-size: 5242880    # 5MB for text messages
    max-binary-message-size: 52428800 # 50MB for binary messages

  # Graceful Shutdown Configuration
  lifecycle:
    timeout-per-shutdown-phase: 30s

# JWT Configuration
jwt:
  secret: 8Zz5tw0Ionm3XPZZfN0NOml3z9FMfmpgXwovR9fp6ryDIoGRM8EPHAB6iHsc0fb
  expiration: 86400000

# Logging Configuration
logging:
  level:
    root: INFO
    "[com.chatapp]": INFO
    "[com.chatapp.service.NotificationService]": DEBUG
    "[com.chatapp.websocket.ChatController]": INFO
    "[com.chatapp.websocket.BinaryFileController]": INFO
    "[com.chatapp.websocket.WebSocketEventListener]": INFO
    "[com.chatapp.service.FileChunkService]": INFO
    "[com.chatapp.config]": WARN
    "[com.chatapp.security]": WARN
    "[org.springframework]":
      web: WARN
      security: WARN
      "[security.access]": WARN
      "[security.web]": WARN
      "[web.socket]": WARN
      "[messaging.simp]": WARN
      "[messaging.simp.broker]": WARN
      "[messaging.simp.stomp]": WARN

# CORS Configuration
cors:
  allowed-origins: "*"
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# File Storage Configuration
app:
  file-storage:
    upload-dir: ${user.dir}/uploads
    max-file-size: 1073741824  # 1GB max file size
    allowed-content-types:
      # Standard image types
      - image/jpeg
      - image/jpg
      - image/png
      - image/gif
      - image/webp
      # Additional image formats (Android camera support)
      - image/heic
      - image/heif
      - image/bmp
      - image/tiff
      - image/tif
      # Android device variations
      - image/pjpeg
      - image/x-png
      # Document types
      - application/pdf
      - application/msword
      - application/vnd.openxmlformats-officedocument.wordprocessingml.document
      - application/vnd.ms-excel
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      - text/plain
      # Audio types
      - audio/mpeg
      - audio/wav
      # Video types
      - video/mp4
      - video/mpeg

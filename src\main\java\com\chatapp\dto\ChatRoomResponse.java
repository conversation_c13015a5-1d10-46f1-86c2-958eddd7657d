package com.chatapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRoomResponse {
    private Long id;
    private String name;
    @JsonProperty("isPrivate")
    private boolean isPrivate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private UserResponse creator;
    private List<UserResponse> participants;
    private MessageResponse lastMessage;
    private int unreadCount;

    // Flat fields for Flutter compatibility
    private String lastMessageContent;
    private String lastMessageSender;
    private LocalDateTime lastMessageTime;
}

# Production Configuration
spring:
  datasource:
    url: ******************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password:
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        "[format_sql]": false
        dialect: org.hibernate.dialect.MySQL8Dialect
  sql:
    init:
      mode: always

# Logging Configuration for Production
logging:
  level:
    root: INFO
    "[com.chatapp]": INFO
    "[org.springframework]":
      web: INFO
      security: INFO
      "[web.socket]": INFO
